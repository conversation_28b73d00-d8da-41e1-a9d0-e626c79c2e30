<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient definitions -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f093fb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f5576c;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="nodeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
    
    <!-- Drop shadow filter -->
    <filter id="dropshadow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3"/>
      <feOffset dx="2" dy="2" result="offset"/>
      <feComponentTransfer>
        <feFuncA type="linear" slope="0.3"/>
      </feComponentTransfer>
      <feMerge> 
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/> 
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="100" cy="100" r="95" fill="url(#primaryGradient)" filter="url(#dropshadow)" opacity="0.1"/>
  
  <!-- Main container circle -->
  <circle cx="100" cy="100" r="85" fill="none" stroke="url(#primaryGradient)" stroke-width="2" opacity="0.3"/>
  
  <!-- Algorithm visualization elements -->
  
  <!-- Binary tree structure -->
  <g transform="translate(100, 40)">
    <!-- Tree connections -->
    <line x1="0" y1="0" x2="-25" y2="25" stroke="url(#primaryGradient)" stroke-width="2" opacity="0.7"/>
    <line x1="0" y1="0" x2="25" y2="25" stroke="url(#primaryGradient)" stroke-width="2" opacity="0.7"/>
    <line x1="-25" y1="25" x2="-40" y2="45" stroke="url(#primaryGradient)" stroke-width="2" opacity="0.7"/>
    <line x1="-25" y1="25" x2="-10" y2="45" stroke="url(#primaryGradient)" stroke-width="2" opacity="0.7"/>
    <line x1="25" y1="25" x2="10" y2="45" stroke="url(#primaryGradient)" stroke-width="2" opacity="0.7"/>
    <line x1="25" y1="25" x2="40" y2="45" stroke="url(#primaryGradient)" stroke-width="2" opacity="0.7"/>
    
    <!-- Tree nodes -->
    <circle cx="0" cy="0" r="8" fill="url(#nodeGradient)" filter="url(#dropshadow)"/>
    <circle cx="-25" cy="25" r="6" fill="url(#accentGradient)" filter="url(#dropshadow)"/>
    <circle cx="25" cy="25" r="6" fill="url(#accentGradient)" filter="url(#dropshadow)"/>
    <circle cx="-40" cy="45" r="5" fill="url(#nodeGradient)" filter="url(#dropshadow)"/>
    <circle cx="-10" cy="45" r="5" fill="url(#nodeGradient)" filter="url(#dropshadow)"/>
    <circle cx="10" cy="45" r="5" fill="url(#nodeGradient)" filter="url(#dropshadow)"/>
    <circle cx="40" cy="45" r="5" fill="url(#nodeGradient)" filter="url(#dropshadow)"/>
  </g>
  
  <!-- Sorting array visualization -->
  <g transform="translate(30, 120)">
    <rect x="0" y="0" width="12" height="30" fill="url(#accentGradient)" rx="2" filter="url(#dropshadow)"/>
    <rect x="15" y="-10" width="12" height="40" fill="url(#primaryGradient)" rx="2" filter="url(#dropshadow)"/>
    <rect x="30" y="5" width="12" height="25" fill="url(#nodeGradient)" rx="2" filter="url(#dropshadow)"/>
    <rect x="45" y="-15" width="12" height="45" fill="url(#accentGradient)" rx="2" filter="url(#dropshadow)"/>
    <rect x="60" y="-5" width="12" height="35" fill="url(#primaryGradient)" rx="2" filter="url(#dropshadow)"/>
    <rect x="75" y="10" width="12" height="20" fill="url(#nodeGradient)" rx="2" filter="url(#dropshadow)"/>
  </g>
  
  <!-- Graph network visualization -->
  <g transform="translate(130, 120)">
    <!-- Graph edges -->
    <line x1="0" y1="0" x2="20" y2="15" stroke="url(#primaryGradient)" stroke-width="2" opacity="0.7"/>
    <line x1="0" y1="0" x2="15" y2="-20" stroke="url(#primaryGradient)" stroke-width="2" opacity="0.7"/>
    <line x1="20" y1="15" x2="40" y2="0" stroke="url(#primaryGradient)" stroke-width="2" opacity="0.7"/>
    <line x1="15" y1="-20" x2="40" y2="0" stroke="url(#primaryGradient)" stroke-width="2" opacity="0.7"/>
    <line x1="20" y1="15" x2="15" y2="-20" stroke="url(#primaryGradient)" stroke-width="2" opacity="0.7"/>
    
    <!-- Graph nodes -->
    <circle cx="0" cy="0" r="6" fill="url(#nodeGradient)" filter="url(#dropshadow)"/>
    <circle cx="20" cy="15" r="6" fill="url(#accentGradient)" filter="url(#dropshadow)"/>
    <circle cx="15" cy="-20" r="6" fill="url(#primaryGradient)" filter="url(#dropshadow)"/>
    <circle cx="40" cy="0" r="6" fill="url(#nodeGradient)" filter="url(#dropshadow)"/>
  </g>
  
  <!-- Central algorithm symbol -->
  <g transform="translate(100, 100)">
    <!-- Hexagonal background -->
    <polygon points="-15,-8.66 -7.5,-15 7.5,-15 15,-8.66 15,8.66 7.5,15 -7.5,15 -15,8.66" 
             fill="url(#primaryGradient)" 
             filter="url(#dropshadow)" 
             opacity="0.9"/>
    
    <!-- Algorithm symbol (stylized A) -->
    <path d="M -6,-6 L 0,-10 L 6,-6 L 4,-2 L 2,-2 L 2,6 L -2,6 L -2,-2 L -4,-2 Z" 
          fill="white" 
          opacity="0.9"/>
    <rect x="-3" y="-1" width="6" height="1.5" fill="white" opacity="0.9"/>
  </g>
  
  <!-- Animated elements (visualization indicators) -->
  <g transform="translate(100, 160)">
    <!-- Progress indicators -->
    <circle cx="-20" cy="0" r="3" fill="url(#accentGradient)" opacity="0.8">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="-10" cy="0" r="3" fill="url(#nodeGradient)" opacity="0.8">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite" begin="0.3s"/>
    </circle>
    <circle cx="0" cy="0" r="3" fill="url(#primaryGradient)" opacity="0.8">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite" begin="0.6s"/>
    </circle>
    <circle cx="10" cy="0" r="3" fill="url(#accentGradient)" opacity="0.8">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite" begin="0.9s"/>
    </circle>
    <circle cx="20" cy="0" r="3" fill="url(#nodeGradient)" opacity="0.8">
      <animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite" begin="1.2s"/>
    </circle>
  </g>
  
  <!-- Decorative elements -->
  <g opacity="0.3">
    <!-- Corner accents -->
    <circle cx="30" cy="30" r="2" fill="url(#accentGradient)"/>
    <circle cx="170" cy="30" r="2" fill="url(#nodeGradient)"/>
    <circle cx="30" cy="170" r="2" fill="url(#nodeGradient)"/>
    <circle cx="170" cy="170" r="2" fill="url(#accentGradient)"/>
  </g>
</svg>
